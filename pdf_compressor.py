#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF压缩工具
支持多种压缩策略，目标是将PDF文件压缩到5MB以内
"""

import os
import sys
from PyPDF2 import PdfReader, PdfWriter
from PIL import Image
import io
import fitz  # PyMuPDF

def get_file_size_mb(file_path):
    """获取文件大小（MB）"""
    return os.path.getsize(file_path) / (1024 * 1024)

def compress_with_pypdf2(input_path, output_path):
    """使用PyPDF2进行基础压缩"""
    try:
        reader = PdfReader(input_path)
        writer = PdfWriter()
        
        for page in reader.pages:
            page.compress_content_streams()
            writer.add_page(page)
        
        with open(output_path, 'wb') as output_file:
            writer.write(output_file)
        
        return True
    except Exception as e:
        print(f"PyPDF2压缩失败: {e}")
        return False

def compress_with_pymupdf(input_path, output_path, quality=50):
    """使用PyMuPDF进行高级压缩"""
    try:
        # 打开PDF文档
        doc = fitz.open(input_path)
        
        # 创建新的PDF
        new_doc = fitz.open()
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # 获取页面的像素图
            mat = fitz.Matrix(1.0, 1.0)  # 保持原始分辨率
            pix = page.get_pixmap(matrix=mat)
            
            # 转换为PIL图像
            img_data = pix.tobytes("png")
            img = Image.open(io.BytesIO(img_data))
            
            # 压缩图像
            img_buffer = io.BytesIO()
            img.save(img_buffer, format='JPEG', quality=quality, optimize=True)
            img_buffer.seek(0)
            
            # 创建新页面并插入压缩后的图像
            new_page = new_doc.new_page(width=page.rect.width, height=page.rect.height)
            new_page.insert_image(page.rect, stream=img_buffer.getvalue())
        
        # 保存压缩后的PDF
        new_doc.save(output_path, garbage=4, deflate=True, clean=True)
        new_doc.close()
        doc.close()
        
        return True
    except Exception as e:
        print(f"PyMuPDF压缩失败: {e}")
        return False

def main():
    input_file = "企业名称核准申请书.pdf"
    
    if not os.path.exists(input_file):
        print(f"错误：找不到文件 {input_file}")
        return
    
    original_size = get_file_size_mb(input_file)
    print(f"原始文件大小: {original_size:.2f} MB")
    
    if original_size <= 5:
        print("文件已经小于5MB，无需压缩")
        return
    
    # 尝试安装PyMuPDF
    try:
        import fitz
    except ImportError:
        print("正在安装PyMuPDF...")
        os.system("pip install PyMuPDF")
        try:
            import fitz
        except ImportError:
            print("无法安装PyMuPDF，使用基础压缩方法")
            # 使用PyPDF2压缩
            output_file = "企业名称核准申请书_压缩.pdf"
            if compress_with_pypdf2(input_file, output_file):
                compressed_size = get_file_size_mb(output_file)
                print(f"压缩后文件大小: {compressed_size:.2f} MB")
                if compressed_size <= 5:
                    print(f"压缩成功！文件已保存为: {output_file}")
                else:
                    print("基础压缩无法达到目标大小")
            return
    
    # 尝试不同的压缩质量
    qualities = [70, 50, 30, 20, 10]
    
    for quality in qualities:
        output_file = f"企业名称核准申请书_压缩_q{quality}.pdf"
        print(f"尝试质量设置 {quality}%...")
        
        if compress_with_pymupdf(input_file, output_file, quality):
            compressed_size = get_file_size_mb(output_file)
            print(f"压缩后文件大小: {compressed_size:.2f} MB")
            
            if compressed_size <= 5:
                print(f"压缩成功！文件已保存为: {output_file}")
                print(f"压缩率: {((original_size - compressed_size) / original_size * 100):.1f}%")
                
                # 删除其他尝试的文件
                for q in qualities:
                    if q != quality:
                        temp_file = f"企业名称核准申请书_压缩_q{q}.pdf"
                        if os.path.exists(temp_file):
                            os.remove(temp_file)
                break
        else:
            if os.path.exists(output_file):
                os.remove(output_file)
    else:
        print("无法将文件压缩到5MB以内，请考虑手动优化或使用专业PDF压缩工具")

if __name__ == "__main__":
    main()
